#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/i2c.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_timer.h"
#include <stdio.h>
#include <string.h>

#ifdef CONFIG_ENABLE_MICROSD_LOGGING
#include "esp_vfs_fat.h"
#include "sdmmc_cmd.h"
#include "driver/spi_common.h"
#include "driver/sdspi_host.h"
#endif

#include "interface.h"

// I2C Bus 0 Configuration (Sensor 1)
#define I2C_BUS_0 I2C_NUM_0
#define I2C_BUS_0_SDA_IO 8 // Default ESP32-S3 I2C SDA pin
#define I2C_BUS_0_SCL_IO 9 // Default ESP32-S3 I2C SCL pin
#define SENSOR1_INT1_PIN 4 // Safe GPIO pin for interrupt

// I2C Bus 1 Configuration (Sensor 2)
#define I2C_BUS_1 I2C_NUM_1
#define I2C_BUS_1_SDA_IO 6  // Safe GPIO pin for I2C SDA
#define I2C_BUS_1_SCL_IO 7  // Safe GPIO pin for I2C SCL
#define SENSOR2_INT1_PIN 12 // Safe GPIO pin for interrupt

#define I2C_MASTER_FREQ_HZ 400000
#define I2C_MASTER_TIMEOUT_MS 1000

// UART Configuration
#define UART_PORT_NUM UART_NUM_0
#define UART_TX_PIN 43
#define UART_RX_PIN 44
#define UART_BUF_SIZE 1024

// ADXL345 Configuration
#define ADXL345_ADDR 0x53

// ADXL345 registers
#define REG_DEVID 0x00
#define REG_BW_RATE 0x2C
#define REG_POWER_CTL 0x2D
#define REG_INT_ENABLE 0x2E
#define REG_INT_MAP 0x2F
#define REG_INT_SOURCE 0x30
#define REG_DATA_FORMAT 0x31
#define REG_DATAX0 0x32
#define REG_FIFO_CTL 0x38
#define REG_FIFO_STATUS 0x39

// ADXL345 interrupt bits
#define INT_WATERMARK 0x02 // Bit 1: FIFO watermark interrupt

// ADXL345 ODR settings: 0x0F = 1600Hz, 0x0E = 800Hz, 0x0D = 400Hz
#define ADXL345_ODR_CODE 0x0E // also see interface.h SAMPLES_PER_SECOND

static const char *TAG = "FIRMW";

// Forward declarations removed - functions reordered instead

// Interrupt handling globals
static volatile bool sensor1_interrupt_flag = false;
static volatile bool sensor2_interrupt_flag = false;

// High priority task handles
static TaskHandle_t sensor1_task_handle = NULL;
static TaskHandle_t sensor2_task_handle = NULL;

#ifdef CONFIG_ENABLE_MICROSD_LOGGING

// Structure for queuing sensor data bursts for SD card logging
typedef struct
{
    uint8_t sensor_id;               // Sensor ID (1 or 2)
    uint8_t sample_count;            // Number of samples in this burst
    int16_t accel[BURST_SAMPLES][3]; // Raw accelerometer data
    uint64_t timestamp_us;           // Timestamp in microseconds since boot
} sd_log_burst_t;

// MicroSD logging globals
static QueueHandle_t sd_log_queue = NULL;
static TaskHandle_t sd_log_task_handle = NULL;
static FILE *csv_file_s1 = NULL;
static FILE *csv_file_s2 = NULL;
static char session_timestamp[32];
static bool sd_logging_enabled = false;

// MicroSD mount point
#define MOUNT_POINT "/sdcard"
#endif

// Write single byte to register for a specific sensor
static esp_err_t write_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, val, true);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Read multiple bytes starting at a register
static esp_err_t read_regs(i2c_port_t i2c_port, uint8_t reg, uint8_t *buf, size_t len)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    // Send register address (with auto-increment bit for multi-byte reads)
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg | (len > 1 ? 0x80 : 0), true);
    // Read bytes
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    if (len > 1)
    {
        i2c_master_read(cmd, buf, len - 1, I2C_MASTER_ACK);
    }
    i2c_master_read_byte(cmd, buf + len - 1, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Removed read_accel_data - using read_accel_burst instead

// Optimized burst read for multiple samples from FIFO
static esp_err_t read_accel_burst(i2c_port_t i2c_port, uint8_t *buf, uint8_t sample_count)
{
    // Read multiple 6-byte samples in one I2C transaction
    // ADXL345 FIFO auto-increments, so we can read continuously
    return read_regs(i2c_port, REG_DATAX0, buf, sample_count * 6);
}

// Read single byte from register
static esp_err_t read_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t *val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, val, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Interrupt service routine for sensor 1 (FIFO watermark)
static void IRAM_ATTR sensor1_isr_handler(void *arg)
{
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    sensor1_interrupt_flag = true;

    // Notify high priority task directly
    if (sensor1_task_handle != NULL)
    {
        vTaskNotifyGiveFromISR(sensor1_task_handle, &xHigherPriorityTaskWoken);
    }

    if (xHigherPriorityTaskWoken)
    {
        portYIELD_FROM_ISR();
    }
}

// Interrupt service routine for sensor 2 (FIFO watermark)
static void IRAM_ATTR sensor2_isr_handler(void *arg)
{
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    sensor2_interrupt_flag = true;

    // Notify high priority task directly
    if (sensor2_task_handle != NULL)
    {
        vTaskNotifyGiveFromISR(sensor2_task_handle, &xHigherPriorityTaskWoken);
    }

    if (xHigherPriorityTaskWoken)
    {
        portYIELD_FROM_ISR();
    }
}

// Initialize a specific I2C bus
static esp_err_t i2c_bus_init(i2c_port_t i2c_port, int sda_io, int scl_io)
{
    i2c_config_t cfg = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = sda_io,
        .scl_io_num = scl_io,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
    };
    esp_err_t err = i2c_param_config(i2c_port, &cfg);
    if (err != ESP_OK)
        return err;
    return i2c_driver_install(i2c_port, cfg.mode, 0, 0, 0);
}

// Initialize all I2C buses
static esp_err_t i2c_master_init(void)
{
    esp_err_t err;

    // Initialize I2C Bus 0
    err = i2c_bus_init(I2C_BUS_0, I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 0: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 0 initialized (SDA=%d, SCL=%d)", I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);

    // Initialize I2C Bus 1
    err = i2c_bus_init(I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 1: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 1 initialized (SDA=%d, SCL=%d)", I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);

    return ESP_OK;
}

// Initialize UART for data transmission
static esp_err_t uart_init(void)
{
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    esp_err_t err = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE, UART_BUF_SIZE, 0, NULL, 0);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_param_config(UART_PORT_NUM, &uart_config);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(err));
        return err;
    }

    ESP_LOGI(TAG, "UART initialized: %d baud, TX=%d, RX=%d", UART_BAUD_RATE, UART_TX_PIN, UART_RX_PIN);
    return ESP_OK;
}

// Initialize GPIO interrupts for ADXL345 sensors
static esp_err_t gpio_interrupt_init(void)
{
    esp_err_t err;

    // Configure GPIO for sensor 1 interrupt (positive edge triggered)
    gpio_config_t io_conf = {
        .pin_bit_mask = (1ULL << SENSOR1_INT1_PIN),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_ENABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_POSEDGE // Positive edge trigger as specified
    };
    err = gpio_config(&io_conf);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure GPIO %d for sensor 1 interrupt", SENSOR1_INT1_PIN);
        return err;
    }

    // Configure GPIO for sensor 2 interrupt (positive edge triggered)
    io_conf.pin_bit_mask = (1ULL << SENSOR2_INT1_PIN);
    err = gpio_config(&io_conf);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure GPIO %d for sensor 2 interrupt", SENSOR2_INT1_PIN);
        return err;
    }

    // Install GPIO ISR service
    err = gpio_install_isr_service(ESP_INTR_FLAG_IRAM);
    if (err != ESP_OK && err != ESP_ERR_INVALID_STATE)
    { // ESP_ERR_INVALID_STATE means already installed
        ESP_LOGE(TAG, "Failed to install GPIO ISR service: %s", esp_err_to_name(err));
        return err;
    }

    // Add ISR handlers for both sensors
    err = gpio_isr_handler_add(SENSOR1_INT1_PIN, sensor1_isr_handler, NULL);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to add ISR handler for sensor 1: %s", esp_err_to_name(err));
        return err;
    }

    err = gpio_isr_handler_add(SENSOR2_INT1_PIN, sensor2_isr_handler, NULL);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to add ISR handler for sensor 2: %s", esp_err_to_name(err));
        return err;
    }

    ESP_LOGI(TAG, "GPIO interrupts initialized: Sensor1 INT1=%d, Sensor2 INT1=%d",
             SENSOR1_INT1_PIN, SENSOR2_INT1_PIN);
    return ESP_OK;
}

// Initialize a specific ADXL345 sensor
static esp_err_t adxl345_init(i2c_port_t i2c_port, uint8_t sensor_id)
{
    uint8_t id = 0;
    esp_err_t err = read_reg(i2c_port, REG_DEVID, &id);

    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read DEVID from sensor %d: %s", sensor_id, esp_err_to_name(err));
        return ESP_FAIL;
    }

    if (id != 0xE5)
    {
        ESP_LOGE(TAG, "Invalid DEVID on sensor %d: expected 0xE5, got 0x%02X", sensor_id, id);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "ADXL345 sensor %d detected (DEVID=0x%02X)", sensor_id, id);

    // Set output data rate to 1600 Hz (0x0F = maximum for ADXL345)
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_BW_RATE, ADXL345_ODR_CODE));
    // ±16 g, full-resolution
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_DATA_FORMAT, 0x0B));
    // Enable measurement mode
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_POWER_CTL, 0x08));

    // FIFO configuration: Stream mode (10 binary = 0x80), watermark = BURST_SAMPLES
    uint8_t fifo_watermark = BURST_SAMPLES;
    // Stream mode: oldest samples are overwritten when FIFO is full
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_FIFO_CTL, 0x80 | fifo_watermark));

    // Configure interrupts: Enable watermark interrupt on INT1
    // First, map watermark interrupt to INT1 (bit 1 = 0 means INT1, bit 1 = 1 means INT2)
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_INT_MAP, 0x00)); // All interrupts to INT1

    // Enable watermark interrupt (bit 1)
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_INT_ENABLE, INT_WATERMARK));

    // Clear any pending interrupts by reading INT_SOURCE
    uint8_t int_source;
    read_reg(i2c_port, REG_INT_SOURCE, &int_source);

    // Verify configuration by reading back registers
    uint8_t bw_rate, data_format, power_ctl, fifo_ctl, int_enable, int_map;
    read_reg(i2c_port, REG_BW_RATE, &bw_rate);
    read_reg(i2c_port, REG_DATA_FORMAT, &data_format);
    read_reg(i2c_port, REG_POWER_CTL, &power_ctl);
    read_reg(i2c_port, REG_FIFO_CTL, &fifo_ctl);
    read_reg(i2c_port, REG_INT_ENABLE, &int_enable);
    read_reg(i2c_port, REG_INT_MAP, &int_map);

    ESP_LOGI(TAG, "Sensor %d config: BW_RATE=0x%02X, DATA_FORMAT=0x%02X, POWER_CTL=0x%02X, FIFO_CTL=0x%02X",
             sensor_id, bw_rate, data_format, power_ctl, fifo_ctl);
    ESP_LOGI(TAG, "Sensor %d interrupts: INT_ENABLE=0x%02X, INT_MAP=0x%02X, INT_SOURCE=0x%02X",
             sensor_id, int_enable, int_map, int_source);
    ESP_LOGI(TAG, "Sensor %d initialized: ±16g @%dHz, FIFO watermark=%d, INT1 enabled",
             sensor_id, SAMPLES_PER_SECOND, fifo_watermark);
    return ESP_OK;
}

// Calculate simple checksum for data integrity
static uint16_t calculate_checksum(const uint8_t *data, size_t len)
{
    uint16_t checksum = 0;
    for (size_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }
    return checksum;
}

// Send sensor data via UART in binary format (variable number of samples)
static void send_sensor_data(uint8_t sensor_id, int16_t accel_data[BURST_SAMPLES][3], uint8_t sample_count)
{
    uart_packet_t packet;

    // Set magic header
    packet.header[0] = 'A';
    packet.header[1] = 'D';
    packet.header[2] = 'X';
    packet.header[3] = 'L';

    // Copy sensor data
    packet.sensor_id = sensor_id;
    packet.sample_count = sample_count;

    // Copy accelerometer data directly
    for (int i = 0; i < sample_count; i++)
    {
        packet.accel[i][0] = accel_data[i][0];
        packet.accel[i][1] = accel_data[i][1];
        packet.accel[i][2] = accel_data[i][2];
    }

    // Calculate checksum (exclude checksum field itself)
    packet.checksum = calculate_checksum((uint8_t *)&packet, sizeof(packet) - sizeof(packet.checksum));

    // Send entire packet in one UART write for maximum efficiency
    int bytes_written = uart_write_bytes(UART_PORT_NUM, &packet, sizeof(packet));
    if (bytes_written != sizeof(packet))
    {
        ESP_LOGW(TAG, "UART write incomplete: %d/%d bytes for sensor %d", bytes_written, sizeof(packet), sensor_id);
    }
}

// Handle FIFO interrupt - read entire FIFO and process data
static void handle_fifo_interrupt(uint8_t sensor_id)
{
    i2c_port_t i2c_port = (sensor_id == 1) ? I2C_BUS_0 : I2C_BUS_1;
    int16_t accel_burst[BURST_SAMPLES][3];
    uint8_t samples_read = 0;

    // Clear interrupt by reading INT_SOURCE register
    uint8_t int_source;
    esp_err_t err = read_reg(i2c_port, REG_INT_SOURCE, &int_source);
    if (err != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to read INT_SOURCE from sensor %d: %s", sensor_id, esp_err_to_name(err));
        return;
    }

    // Check FIFO status
    uint8_t fifo_status;
    err = read_reg(i2c_port, REG_FIFO_STATUS, &fifo_status);
    if (err != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to read FIFO status from sensor %d: %s", sensor_id, esp_err_to_name(err));
        return;
    }

    // Check for FIFO overflow and reset if needed
    if (fifo_status & 0x80)
    {
        ESP_LOGW(TAG, "FIFO overflow on sensor %d! Resetting FIFO...", sensor_id);
        write_reg(i2c_port, REG_FIFO_CTL, 0x00); // Disable FIFO
        vTaskDelay(pdMS_TO_TICKS(1));
        write_reg(i2c_port, REG_FIFO_CTL, 0x80 | BURST_SAMPLES); // Re-enable stream mode
        return;
    }

    uint8_t samples_available = fifo_status & 0x3F;

    // Read the entire FIFO in one burst operation
    if (samples_available > 0)
    {
        // Limit to BURST_SAMPLES to avoid buffer overflow
        uint8_t samples_to_read = (samples_available > BURST_SAMPLES) ? BURST_SAMPLES : samples_available;

        // Allocate buffer for burst read (6 bytes per sample)
        uint8_t raw_burst[BURST_SAMPLES * 6];

        // Read all samples in one I2C transaction
        err = read_accel_burst(i2c_port, raw_burst, samples_to_read);
        if (err == ESP_OK)
        {
            // Convert raw data to signed 16-bit values
            for (int i = 0; i < samples_to_read; i++)
            {
                uint8_t *sample_data = &raw_burst[i * 6];
                accel_burst[i][0] = (int16_t)((sample_data[1] << 8) | sample_data[0]);
                accel_burst[i][1] = (int16_t)((sample_data[3] << 8) | sample_data[2]);
                accel_burst[i][2] = (int16_t)((sample_data[5] << 8) | sample_data[4]);
            }
            samples_read = samples_to_read;

            // Send data
            send_sensor_data(sensor_id, accel_burst, samples_read);

#ifdef CONFIG_ENABLE_MICROSD_LOGGING
            // Queue data for SD card logging
            queue_for_sd_logging(sensor_id, accel_burst, samples_read);
#endif
        }
        else
        {
            ESP_LOGW(TAG, "Failed to read burst from sensor %d: %s", sensor_id, esp_err_to_name(err));
        }
    }
}

// High priority task for sensor 1 interrupt handling
static void sensor1_interrupt_task(void *pvParameters)
{
    while (1)
    {
        // Wait for interrupt notification
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);

        // Handle the FIFO interrupt
        handle_fifo_interrupt(1);
    }
}

// High priority task for sensor 2 interrupt handling
static void sensor2_interrupt_task(void *pvParameters)
{
    while (1)
    {
        // Wait for interrupt notification
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);

        // Handle the FIFO interrupt
        handle_fifo_interrupt(2);
    }
}

#ifdef CONFIG_ENABLE_MICROSD_LOGGING
// Initialize microSD card
static esp_err_t init_microsd(void)
{
    ESP_LOGI(TAG, "Initializing microSD card");

    esp_vfs_fat_sdmmc_mount_config_t mount_config = {
        .format_if_mount_failed = false,
        .max_files = 5,
        .allocation_unit_size = 16 * 1024};

    sdmmc_card_t *card;
    const char mount_point[] = MOUNT_POINT;

    ESP_LOGI(TAG, "Initializing SD card using SPI peripheral");

    sdmmc_host_t host = SDSPI_HOST_DEFAULT();
    host.slot = CONFIG_MICROSD_SPI_HOST;

    spi_bus_config_t bus_cfg = {
        .mosi_io_num = CONFIG_MICROSD_PIN_MOSI,
        .miso_io_num = CONFIG_MICROSD_PIN_MISO,
        .sclk_io_num = CONFIG_MICROSD_PIN_CLK,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = 4000,
    };

    esp_err_t ret = spi_bus_initialize(host.slot, &bus_cfg, SDSPI_DEFAULT_DMA);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize SPI bus: %s", esp_err_to_name(ret));
        return ret;
    }

    sdspi_device_config_t slot_config = SDSPI_DEVICE_CONFIG_DEFAULT();
    slot_config.gpio_cs = CONFIG_MICROSD_PIN_CS;
    slot_config.host_id = host.slot;

    ret = esp_vfs_fat_sdspi_mount(mount_point, &host, &slot_config, &mount_config, &card);
    if (ret != ESP_OK)
    {
        if (ret == ESP_FAIL)
        {
            ESP_LOGE(TAG, "Failed to mount filesystem. If you want the card to be formatted, set format_if_mount_failed = true.");
        }
        else
        {
            ESP_LOGE(TAG, "Failed to initialize the card (%s). Make sure SD card lines have pull-up resistors in place.", esp_err_to_name(ret));
        }
        spi_bus_free(host.slot);
        return ret;
    }

    // Card has been initialized, print its properties
    sdmmc_card_print_info(stdout, card);
    ESP_LOGI(TAG, "MicroSD card mounted successfully");
    return ESP_OK;
}

// Create session timestamp and initialize CSV files
static esp_err_t init_csv_files(void)
{
    // Create timestamp for this session
    int64_t time_us = esp_timer_get_time();
    int64_t time_s = time_us / 1000000;
    int64_t time_ms = (time_us % 1000000) / 1000;

    // Format: YYYY-MM-DD_HH-MM-SS (simplified, using boot time)
    snprintf(session_timestamp, sizeof(session_timestamp), "boot_%lld_%03lld", time_s, time_ms);

    // Create CSV file paths
    char csv_path_s1[128];
    char csv_path_s2[128];
    char json_path[128];

    snprintf(csv_path_s1, sizeof(csv_path_s1), MOUNT_POINT "/readings-%s-s1.csv", session_timestamp);
    snprintf(csv_path_s2, sizeof(csv_path_s2), MOUNT_POINT "/readings-%s-s2.csv", session_timestamp);
    snprintf(json_path, sizeof(json_path), MOUNT_POINT "/metadata-%s.json", session_timestamp);

    // Create CSV files
    csv_file_s1 = fopen(csv_path_s1, "w");
    if (!csv_file_s1)
    {
        ESP_LOGE(TAG, "Failed to create CSV file for sensor 1");
        return ESP_FAIL;
    }

    csv_file_s2 = fopen(csv_path_s2, "w");
    if (!csv_file_s2)
    {
        ESP_LOGE(TAG, "Failed to create CSV file for sensor 2");
        fclose(csv_file_s1);
        csv_file_s1 = NULL;
        return ESP_FAIL;
    }

    // Write CSV headers
    fprintf(csv_file_s1, "time_offset,x,y,z\n");
    fprintf(csv_file_s2, "time_offset,x,y,z\n");
    fflush(csv_file_s1);
    fflush(csv_file_s2);

    // Create metadata JSON file (simple format, no external JSON library)
    FILE *json_file = fopen(json_path, "w");
    if (json_file)
    {
        fprintf(json_file, "{\n");
        fprintf(json_file, "  \"scenario\": \"firmware_logging\",\n");
        fprintf(json_file, "  \"defect\": false,\n");
        fprintf(json_file, "  \"defect_kind\": null,\n");
        fprintf(json_file, "  \"batch_start\": \"%s\",\n", session_timestamp);
        fprintf(json_file, "  \"scenario_start\": \"%s\",\n", session_timestamp);
        fprintf(json_file, "  \"hostname\": \"esp32s3-firmware\",\n");
        fprintf(json_file, "  \"source\": \"microsd_logging\",\n");
        fprintf(json_file, "  \"sensor1_file\": \"readings-%s-s1.csv\",\n", session_timestamp);
        fprintf(json_file, "  \"sensor2_file\": \"readings-%s-s2.csv\"\n", session_timestamp);
        fprintf(json_file, "}\n");
        fclose(json_file);
    }

    ESP_LOGI(TAG, "CSV files created: %s, %s", csv_path_s1, csv_path_s2);
    return ESP_OK;
}

// SD card logging task
static void sd_logging_task(void *pvParameters)
{
    ESP_LOGI(TAG, "SD logging task started");
    sd_log_burst_t burst;
    int64_t session_start_us = esp_timer_get_time();

    while (1)
    {
        if (xQueueReceive(sd_log_queue, &burst, portMAX_DELAY) == pdTRUE)
        {
            FILE *csv_file = (burst.sensor_id == 1) ? csv_file_s1 : csv_file_s2;

            if (csv_file)
            {
                // Calculate time offset for each sample in the burst
                // Samples are spaced by 1/SAMPLES_PER_SECOND seconds
                uint64_t sample_interval_us = 1000000 / SAMPLES_PER_SECOND;

                for (int i = 0; i < burst.sample_count; i++)
                {
                    // Calculate timestamp for this sample (working backwards from burst timestamp)
                    uint64_t sample_time_us = burst.timestamp_us - (burst.sample_count - 1 - i) * sample_interval_us;
                    uint64_t time_offset_us = sample_time_us - session_start_us;

                    // Write CSV line: time_offset,x,y,z
                    fprintf(csv_file, "%llu,%d,%d,%d\n",
                            time_offset_us,
                            burst.accel[i][0],
                            burst.accel[i][1],
                            burst.accel[i][2]);
                }

                // Flush periodically to ensure data is written
                static int flush_counter = 0;
                if (++flush_counter >= 10)
                {
                    fflush(csv_file);
                    flush_counter = 0;
                }
            }
        }
    }
}

// Queue sensor data burst for SD card logging
static void queue_for_sd_logging(uint8_t sensor_id, int16_t accel_data[BURST_SAMPLES][3], uint8_t sample_count)
{
    if (!sd_logging_enabled || !sd_log_queue)
    {
        return;
    }

    sd_log_burst_t burst;
    burst.sensor_id = sensor_id;
    burst.sample_count = sample_count;
    burst.timestamp_us = esp_timer_get_time();

    // Copy accelerometer data
    for (int i = 0; i < sample_count; i++)
    {
        burst.accel[i][0] = accel_data[i][0];
        burst.accel[i][1] = accel_data[i][1];
        burst.accel[i][2] = accel_data[i][2];
    }

    // Try to send to queue (non-blocking)
    if (xQueueSend(sd_log_queue, &burst, 0) != pdTRUE)
    {
        // Queue is full, drop this burst
        static uint32_t drop_counter = 0;
        if (++drop_counter % 100 == 0)
        {
            ESP_LOGW(TAG, "SD logging queue full, dropped %lu bursts", drop_counter);
        }
    }
}
#endif

void app_main(void)
{
    ESP_LOGI(TAG, "Starting high-performance dual ADXL345 sensor firmware");

    // Initialize I2C buses
    ESP_ERROR_CHECK(i2c_master_init());

    // Initialize UART for data transmission
    ESP_ERROR_CHECK(uart_init());

    // Initialize GPIO interrupts
    ESP_ERROR_CHECK(gpio_interrupt_init());

    // Initialize both sensors
    ESP_LOGI(TAG, "Initializing sensors...");
    if (adxl345_init(I2C_BUS_0, 1) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor 1");
        // sleep
        vTaskDelay(pdMS_TO_TICKS(1000));
        abort();
    }

    if (adxl345_init(I2C_BUS_1, 2) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor 2");
        vTaskDelay(pdMS_TO_TICKS(1000));
        abort();
    }

    ESP_LOGI(TAG, "Both sensors initialized successfully");

#ifdef CONFIG_ENABLE_MICROSD_LOGGING
    // Initialize microSD logging if enabled
    ESP_LOGI(TAG, "MicroSD logging is enabled, initializing...");
    if (init_microsd() == ESP_OK && init_csv_files() == ESP_OK)
    {
        // Create queue for SD logging
        sd_log_queue = xQueueCreate(CONFIG_MICROSD_BURST_QUEUE_SIZE, sizeof(sd_log_burst_t));
        if (sd_log_queue)
        {
            // Create SD logging task
            BaseType_t task_created = xTaskCreate(
                sd_logging_task,
                "sd_log_task",
                4096, // Stack size
                NULL,
                5, // Priority
                &sd_log_task_handle);

            if (task_created == pdPASS)
            {
                sd_logging_enabled = true;
                ESP_LOGI(TAG, "MicroSD logging initialized successfully");
            }
            else
            {
                ESP_LOGE(TAG, "Failed to create SD logging task");
                vQueueDelete(sd_log_queue);
                sd_log_queue = NULL;
            }
        }
        else
        {
            ESP_LOGE(TAG, "Failed to create SD logging queue");
        }
    }
    else
    {
        ESP_LOGE(TAG, "Failed to initialize microSD logging");
    }
#else
    ESP_LOGI(TAG, "MicroSD logging is disabled");
#endif

    // Create high priority tasks for interrupt handling
    BaseType_t ret1 = xTaskCreate(sensor1_interrupt_task, "sensor1_int", 4096, NULL,
                                  15, &sensor1_task_handle);
    BaseType_t ret2 = xTaskCreate(sensor2_interrupt_task, "sensor2_int", 4096, NULL,
                                  15, &sensor2_task_handle);

    if (ret1 != pdPASS || ret2 != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create interrupt handling tasks");
        return;
    }

    ESP_LOGI(TAG, "High priority interrupt tasks created");
    ESP_LOGI(TAG, "Starting interrupt-driven FIFO processing @%dHz per sensor", SAMPLES_PER_SECOND);

    uint32_t last_log_time = 0;

    // Main loop - just monitoring and stats
    while (1)
    {
        // Log performance stats every second
        uint32_t current_time = xTaskGetTickCount();
        if (current_time - last_log_time >= pdMS_TO_TICKS(1000))
        {
            // Note: In this simplified version, we don't track exact sample counts
            // The high priority tasks handle the actual data processing
            ESP_LOGI(TAG, "Interrupt-driven FIFO processing active - high priority tasks handling data");
            last_log_time = current_time;
        }

        // Sleep for 1 second - high priority tasks handle all the real work
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}
